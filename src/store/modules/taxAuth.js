export default {
    namespaced: true,
    state: () => ({
        taxBelongId: '',
        taxBelongName: ''
    }),
    mutations: {
        setTaxAuth(state, { id, name }) {
            state.taxBelongId = id;
            state.taxBelongName = name;
        }
    }
}

// ========== 工具函数与常量 ========== //
const noFilterIds = [
    '26ff71ec665b4557b4fe64ff1f7d469d', // 企易鑫总部
    '2b669683d57741b3b808e092698fa204', // 企易鑫
    'a7b0f471c7ed47b6b300713e08fc86c8', // 北京星火基石OEM
    'e0c3d18be1424fdcb350fc57b191fe59', // 江苏金卫士OEM
    '1ee4762138024eb8b54872a1e738267f', // 汇优财OEM
    '20862bae7f3c4fc3b77d74eb8f9b9abd'  // 湖北优奕OEM
];

const stopwords = [
    '有限责任公司', '有限公司', '股份公司', '分公司', '公司', '集团',
    '云计算', '信息', '科技', '技术', '企业', '管理', '咨询', '数字', '互联网', '服务', '网络',
    '数据', '通信', '市场', '营销', '广告', '渠道', '卫星', '交通', '运输', '人力', '资源', '劳务',
];

function extractKeyword(taxName) {
    const provinceCityPattern = /^(北京|天津|上海|重庆|河北|山西|辽宁|吉林|黑龙江|江苏|浙江|安徽|福建|江西|山东|河南|湖北|湖南|广东|海南|四川|贵州|云南|陕西|甘肃|青海|台湾|内蒙古|广西|西藏|宁夏|新疆|香港|澳门)/;
    let cleaned = taxName.replace(provinceCityPattern, '');
    cleaned = cleaned.replace(/(省|市|自治区|特别行政区)/g, '');
    cleaned = cleaned.replace(/（.*?）|\(.*?\)|\[.*?\]/g, '');
    stopwords.forEach(word => {
        cleaned = cleaned.replace(new RegExp(word, 'g'), 'x');
    });
    cleaned = cleaned.replace(/x+/g, 'x');
    cleaned = cleaned.replace(/^x+|x+$/g, '').trim();
    let match = cleaned.match(/[^x\s][^x\s]*/);
    let keyword = match ? match[0] : '';
    let segs = [];
    for (let i = 0; i < keyword.length - 1; i++) {
        segs.push(keyword.slice(i, i + 2));
    }
    if (keyword.length === 1) segs.push(keyword);
    return segs;
}

/**
 * 通用税地权限过滤
 * @param {Array} list - 需要过滤的数据列表
 * @param {String} taxBelongId - 当前税地ID
 * @param {String} taxBelongName - 当前税地名称
 * @param {String} field - 匹配字段（如'company'、'name'等）
 * @returns {Array}
 */
export function filteredByTaxAuth({ list, taxBelongId, taxBelongName, field = 'company' }) {
    if (!list) return [];
    if (noFilterIds.includes(taxBelongId)) return list;
    if (!taxBelongName) return [];
    const segs = extractKeyword(taxBelongName);
    if (!segs.length) return [];
    return list.filter(item =>
        item[field] && segs.some(seg => item[field].includes(seg))
    );
}
