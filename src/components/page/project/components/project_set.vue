<template>
    <div>
        <el-dialog :title="projectInfo ? '项目设置' : '项目新增'" :visible.sync="toshowDia" @close="closeDia">
            <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="120px" class="demo-ruleForm">
                <el-form-item label="企业名称" prop="enterpriseId" v-if="!projectInfo">
                    <el-select v-model="ruleForm.enterpriseId" filterable placeholder="请选择" @change="handleChange">
                        <el-option v-for="item in options" :key="item.enterpriseId" :label="item.enterpriseName" :value="item.enterpriseId">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="项目名称" prop="name">
                    <el-input v-model="ruleForm.name"></el-input>
                </el-form-item>
                <el-form-item label="项目描述" prop="description">
                    <el-input v-model="ruleForm.description" type="textarea" placeholder="请输入项目描述"></el-input>
                </el-form-item>
                <el-form-item label="项目人数" v-if="showCrowdSwitch">
                    <el-input v-model="ruleForm.workerNumber" type="number" placeholder="请输入项目人数"></el-input>
                </el-form-item>
                <el-form-item label="项目报酬" prop="reward" v-if="showCrowdSwitch">
                    <el-col :span="16">
                        <el-input v-model="ruleForm.reward" type="number" placeholder="请输入报酬金额"><template slot="append">元</template></el-input>
                    </el-col>
                    <el-col :span="7" :offset="1">
                        <el-select v-model="ruleForm.rewardRule" placeholder="请选择结算方式">
                            <el-option label="人/项目" :value="1"></el-option>
                            <el-option label="人/小时" :value="2"></el-option>
                            <el-option label="人/天" :value="3"></el-option>
                            <el-option label="人/周" :value="4"></el-option>
                            <el-option label="人/月" :value="5"></el-option>
                            <el-option label="人/季" :value="6"></el-option>
                            <el-option label="人/年" :value="7"></el-option>
                        </el-select>
                    </el-col>
                </el-form-item>
                <el-form-item label="项目周期" v-if="showCrowdSwitch">
                    <el-col :span="11">
                        <el-date-picker type="date" placeholder="请选择开始日期" format="yyyy 年 MM 月 dd 日" value-format="yyyy-MM-dd" v-model="ruleForm.startDate" style="width: 100%;"></el-date-picker>
                    </el-col>
                    <el-col class="line" :span="2" style="text-align: center;">-</el-col>
                    <el-col :span="11">
                    <el-date-picker type="date" placeholder="请选择结束日期" format="yyyy 年 MM 月 dd 日" value-format="yyyy-MM-dd" v-model="ruleForm.endDate" style="width: 100%;"></el-date-picker>
                    </el-col>
                </el-form-item>
                <el-form-item prop="invoiceCategoryIds" v-if="showCrowdSwitch">
                    <span slot="label">
                        开票类目
                        <el-tooltip content="设置项目在指定税地中发放时可开具的发票类目" placement="top">
                            <i class="el-icon-question" style="color: #909399; cursor: help;"></i>
                        </el-tooltip>
                    </span>
                    <el-select
                        v-model="validInvoiceCategoryIds"
                        multiple
                        placeholder="请选择开票类目"
                        style="width: 100%;"
                        filterable
                        clearable>
                        <el-option
                            v-for="category in invoiceCategoryList"
                            :key="category.id"
                            :label="category.name + ' (' + category.taxesName + ')'"
                            :value="category.id">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="众包大厅展示" v-if="showCrowdSwitch">
                    <el-col :span="12">
                        <el-select v-model="ruleForm.openOption" placeholder="请选择众包大厅展示选项">
                            <el-option label="不可展示和搜索" :value="0"></el-option>
                            <el-option label="可展示和搜索" :value="1"></el-option>
                            <el-option label="不展示，仅可搜索" :value="2"></el-option>
                        </el-select>
                    </el-col>
                    <el-col :span="12">
                        <el-checkbox v-model="ruleForm.canApply">允许众包大厅用户申请</el-checkbox>
                    </el-col>
                </el-form-item>
                <el-form-item label="地点">
                    <el-col :span="8">
                        <el-form-item prop="province">
                            <el-select v-model="ruleForm.province" @change="handleProvince" filterable placeholder="请选择省">
                                <el-option :label="item.name" :value="item.code" v-for="(item, index) in provinceList" :key="index"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item prop="city">
                            <el-select v-model="ruleForm.city" @change="handleCity" filterable placeholder="请选择城市">
                                <el-option :label="item.name" :value="item.code" v-for="(item, index) in cityList" :key="index"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item prop="area">
                            <el-select v-model="ruleForm.area" @change="handleArea" filterable placeholder="请选择区/县">
                                <el-option :label="item.name" :value="item.code" v-for="(item, index) in areaList" :key="index"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-form-item>
                <el-form-item label="详细地址">
                    <el-input v-model="ruleForm.address" />
                </el-form-item>
                <el-form-item label="项目地点外景图">
                    <div class="upload_box">
                        <div class="pic_list" v-for="item in fileLists" :key="item.url">
                            <el-image
                                style="width: 146px; height: 146px; margin-right: 15px; border-radius: 5px; display: block"
                                :src="ossUrl + item.url"
                            >
                            </el-image>
                            <i class="el-icon-delete icon_size" @click="toRemovePic(item.url)"></i>
                        </div>
                        <el-upload
                            action=""
                            list-type="picture-card"
                            :http-request="uploadURL"
                            :on-success="uploadSuccess"
                            :on-error="uploadError"
                            :multiple="true"
                            :show-file-list="false"
                            :before-upload="handleBeforeUpload"
                        >
                            <i class="el-icon-plus"></i>
                        </el-upload>
                    </div>
                </el-form-item>
                <el-form-item prop="agree" :rules="[{ validator: validateAgree, trigger: 'change' }]">
                    <el-checkbox class="custom-checkbox" v-model="ruleForm.agree">我司承诺本项目不涉及淫秽色情、谣言、暴恐、赌博、诈骗、洗钱等违法内容，一旦发现，立即关停账号，并依法交由相关执法机关调查处理</el-checkbox>
                </el-form-item>
            </el-form>
            <div class="button_box">
                <el-button type="danger" v-if="projectInfo" @click="toRemoveProject">解散项目</el-button>
                <div>
                    <el-button @click="closeDia">取消</el-button>
                    <el-button type="primary" @click="toSureAdd">确定</el-button>
                </div>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import { Message } from 'element-ui';
import { client, getFileNameUUID, getTimeNow } from '../../../../utils/oss.js';
import { amapManager } from 'vue-amap';
import { getProvince, getCity, getArea } from '@/api/area.js';
import { deleteProject, isShowCrowd } from '@/api/project/project.js';
import { queryInvoiceCategoriesByEnterpriseId } from '@/api/branch/enterprise.js';
import saveCode from '@/components/common/Savecode.vue';
import { OSS_URL } from '@/api/config';
export default {
    data() {
        return {
            toshowDia: false,
            ruleForm: {
                name: '',
                openOption: 0,
                canApply: true,
                province: '',
                agree: false,
                invoiceCategoryIds: []
            },
            rules: {
                enterpriseId: [{ required: true, message: '请选择企业', trigger: 'change' }],
                name: [
                    { required: true, message: '请输入项目名称', trigger: 'blur' },
                    { min: 1, max: 40, message: '项目名称最多40个字', trigger: 'blur' }
                ],
                description: [
                    { required: true, message: '请输入项目描述', trigger: 'blur' },
                    { min: 10, max: 200, message: '项目描述不少于10字，最多200个字', trigger: 'blur' }
                ],
                invoiceCategoryIds: [
                    { required: true, message: '请选择开票类目', trigger: 'blur' }
                ],
            },
            provinceList: [],
            cityList: [],
            areaList: [],
            zoom: 12,
            markers: [],
            searchOption: {},
            amapManager,
            events: {
                init: (o) => {
                    // console.log(o.getCenter());
                    // console.log(this.$refs.map.$$getInstance());
                    // o.getCity(result => {
                    //   console.log(result);
                    // });
                },
                moveend: () => {},
                zoomchange: () => {},
                click: (e) => {
                    console.log('click map', e);
                    this.markers = [];
                    this.addMarker(e.lnglat.lng, e.lnglat.lat);
                    this.handlerSelect(e.lnglat);
                },
                placeSearch: {}
            },
            mapCenter: [121.60408, 29.851345],
            fileList: [],
            fileLists: [],
            dialogSaveCodes: false,
            ossUrl: OSS_URL,
            invoiceCategoryList: []
        };
    },
    components: {
        saveCode
    },
    computed: {
        // 确保只显示有效的开票类目ID
        validInvoiceCategoryIds: {
            get() {
                if (this.ruleForm.invoiceCategoryIds && this.invoiceCategoryList.length > 0) {
                    const validIds = this.invoiceCategoryList.map(item => item.id);
                    return this.ruleForm.invoiceCategoryIds.filter(id => validIds.includes(id));
                }
                return this.ruleForm.invoiceCategoryIds;
            },
            set(value) {
                this.ruleForm.invoiceCategoryIds = value;
            }
        }
    },
    props: {
        dialogVisible: {
            type: Boolean,
            require: true
        },
        projectInfo: {
            type: Object,
            require: false
        },
        showCrowdSwitch: {
            type: Boolean,
            require: false
        },
        options: {
            type: Array,
            require: true
        },
        enterpriseId: {
            type: String,
            require: false
        }
    },
    watch: {
        enterpriseId: {
            handler(newVal) {
                console.log("enterpriseId: ", newVal);
                if (newVal) {
                    queryInvoiceCategoriesByEnterpriseId(newVal).then((res) => {
                        if (res.data.code === 0) {
                            this.invoiceCategoryList = res.data.data;
                            // 过滤掉不存在的invoiceCategoryIds
                            this.filterInvalidInvoiceCategoryIds();
                        }
                    });
                }
            }
        },
        dialogVisible: {
            handler(newVal) {
                this.toshowDia = newVal;
                this.ruleForm = {
                    name: '',
                    openOption: 0,
                    canApply: true,
                    province: '',
                    agree: false
                };
                if(this.showCrowdSwitch) {
                    this.ruleForm.openOption = 1;
                }
            }
        },
        projectInfo: {
            handler(newVal) {
                console.log('projectInfo: ', newVal);
                if (newVal) {
                    this.ruleForm = newVal;
                    this.ruleForm.province = newVal.provinceCode;
                    getCity({
                        areaCode: newVal.provinceCode
                    }).then((res) => {
                        if (res.data.code === 0) {
                            this.cityList = res.data.data;
                            this.ruleForm.city = newVal.cityCode;
                        }
                    });
                    getArea({
                        cityCode: newVal.cityCode
                    }).then((res) => {
                        if (res.data.code === 0) {
                            this.areaList = res.data.data;
                            this.ruleForm.area = newVal.areaCode;
                        }
                    });
                    if (newVal.fileList) {
                        this.fileLists = newVal.fileList;
                    }
                }
            }
        },
        showCrowdSwitch: {
            handler(newVal) {
                if (newVal) {
                    this.ruleForm.openOption = 1;
                }
            }
        }
    },
    mounted() {
        // this.placeSearch = new AMap.PlaceSearch(); //构造地点查询类
        // console.log("gdMap component mounted");
        if (this.initPos) {
            this.mapCenter = [this.initPos.lng, this.initPos.lat];
            this.markers = [];
            this.addMarker(this.initPos.lng, this.initPos.lat);
        }
        getProvince().then((res) => {
            if (res.data.code === 0) {
                this.provinceList = res.data.data;
            } else {
                Message({
                    message: res.data.msg,
                    type: 'error'
                });
            }
        });
    },
    methods: {
        closeCodeDia() {
            this.dialogSaveCodes = false;
        },
        closeDia() {
            this.$emit('closeDia');
        },
        handleChange(value) {
            this.showCrowd(value);
        },
        showCrowd(enterpriseId) {
            isShowCrowd({
                enterpriseId: enterpriseId,
                projectId: ''
            }).then((res) => {
                if (res.data.code == 0) {
                    this.showCrowdSwitch = res.data.data;
                }
            });
            if (enterpriseId) {
                queryInvoiceCategoriesByEnterpriseId(enterpriseId).then((res) => {
                    if (res.data.code === 0) {
                        this.invoiceCategoryList = res.data.data;
                        // 过滤掉不存在的invoiceCategoryIds
                        this.filterInvalidInvoiceCategoryIds();
                    }
                });
            }
        },
        onSearchResult(pois) {
            console.log('onSearchResult pois', pois);
            this.markers = [];
            let latSum = 0;
            let lngSum = 0;
            if (pois.length > 0) {
                pois.forEach((poi) => {
                    const { lng, lat } = poi;
                    lngSum += lng;
                    latSum += lat;
                    this.addMarker(poi.lng, poi.lat);
                    this.ruleForm.longitude = poi.lng;
                    this.ruleForm.latitude = poi.lat;
                });
                const center = {
                    lng: lngSum / pois.length,
                    lat: latSum / pois.length
                };
                this.mapCenter = [center.lng, center.lat];
            }
            this.$emit('search', pois);
        },
        addMarker(lng, lat) {
            const marker = {
                position: [lng, lat],
                events: {
                    click: (e) => {
                        console.log('click marker e', e);
                        this.handlerSelect(e.lnglat);
                    },
                    dragend: (e) => {
                        console.log('---event---: dragend');
                        this.markers[0].position = [e.lnglat.lng, e.lnglat.lat];
                    }
                }
            };
            this.markers.push(marker);
        },
        handlerSelect(loc) {
            new AMap.Geocoder().getAddress(loc, (status, result) => {
                console.log(result);
                console.log('getAddress', loc, status, result);
                this.ruleForm.longitude = loc.lng;
                this.ruleForm.latitude = loc.lat;
                if (status == 'complete') {
                    this.$emit('tap', {
                        loc,
                        address: result.regeocode.formattedAddress
                    });
                }
                this.$forceUpdate();
            });
        },
        handleProvince() {
            this.ruleForm.city = '';
            this.ruleForm.area = '';
            this.areaList = [];
            getCity({
                areaCode: this.ruleForm.province
            }).then((res) => {
                if (res.data.code === 0) {
                    this.cityList = res.data.data;
                }
            });
        },
        handleCity() {
            this.ruleForm.area = '';
            getArea({
                cityCode: this.ruleForm.city
            }).then((res) => {
                if (res.data.code === 0) {
                    this.areaList = res.data.data;
                }
            });
            this.$forceUpdate();
        },
        handleArea() {
            this.$forceUpdate();
        },
        handleBeforeUpload(file) {
            const isJPEG = file.name.split('.')[1] === 'jpeg';
            const isJPG = file.name.split('.')[1] === 'jpg';
            const isPNG = file.name.split('.')[1] === 'png';
            const isWEBP = file.name.split('.')[1] === 'webp';
            const isGIF = file.name.split('.')[1] === 'gif';
            const isLt500K = file.size / 1024 / 1024 / 1024 / 1024 < 4;
            if (!isJPG && !isJPEG && !isPNG && !isWEBP && !isGIF) {
                this.$message.error('上传图片只能是 JPEG/JPG/PNG 格式!');
            }
            if (!isLt500K) {
                this.$message.error('单张图片大小不能超过 4mb!');
            }
            return (isJPEG || isJPG || isPNG || isWEBP || isGIF) && isLt500K;
        },
        uploadSuccess(res) {
            console.log('success: ', res);
        },
        uploadError(err) {
            console.log('error: ', err);
            this.$message.error('上传失败！请重试');
        },
        uploadURL(option) {
            //注意哦，这里指定文件夹'image/'，尝试过写在配置文件，但是各种不行，写在这里就可以
            var suffix = option.file.name.substring(option.file.name.lastIndexOf('.'));
            var fileName = '/project/' + getTimeNow() + '/' + getFileNameUUID() + suffix;
            //定义唯一的文件名，打印出来的uid其实就是时间戳
            client()
                .multipartUpload(fileName, option.file, {
                    headers: {
                        'Content-Type': 'image/jpg'
                    },
                    progress: function (percentage, cpt) {
                        option.onProgress({ percent: Math.floor(percentage * 100)});
                    }
                })
                .then((res) => {
                    if(res.res.status === 200) {
                        this.fileLists.push({
                            name: option.file.name,
                            url: fileName
                        });
                        option.onSuccess(res);
                    }else {
                        option.onError(res);
                    }
                }).catch((err) => {
                    option.onError(err);
                });
        },
        toRemovePic(url) {
            this.fileLists.forEach((v, index) => {
                if (url == v.url) {
                    this.fileLists.splice(index, 1);
                }
            });
        },
        validateAgree(rule, value, callback) {
            if (!value) {
                callback(new Error('您必须同意此条款'));
            } else {
                callback();
            }
        },
        toSureAdd() {
            if(this.ruleForm.reward && parseFloat(this.ruleForm.reward) > 0) {
                if(!this.ruleForm.rewardRule) return Message.error('请选择结算方式')
            }
            let data = {
                ...this.ruleForm,
                fileList: this.fileLists
            };
            this.$refs.ruleForm.validate((valid) => {
                if (valid) {
                    console.log(data);
                    this.$emit('toAddProject', data);
                } else {
                    return false;
                }
            });
        },
        toRemoveProject() {
            this.$saveCode()
                .then((res) => {
                    deleteProject({
                        id: this.projectInfo.projectId,
                        password: res.password
                    }).then((res) => {
                        if (res.data.code == 0) {
                            Message({
                                message: '删除成功！',
                                type: 'success'
                            });
                            this.$router.go(-1);
                        }
                    });
                })
                .catch(() => {});
        },
        // 过滤掉不存在的invoiceCategoryIds
        filterInvalidInvoiceCategoryIds() {
            if (this.ruleForm.invoiceCategoryIds && this.ruleForm.invoiceCategoryIds.length > 0) {
                const validIds = this.invoiceCategoryList.map(item => item.id);
                const filteredIds = this.ruleForm.invoiceCategoryIds.filter(id => validIds.includes(id));

                // 如果过滤后的数组与原数组不同，说明有无效的ID被移除
                if (filteredIds.length !== this.ruleForm.invoiceCategoryIds.length) {
                    this.ruleForm.invoiceCategoryIds = filteredIds;
                    console.log('已过滤掉无效的开票类目ID');
                }
            }
        }
    }
};
</script>

<style scoped>
.button_box {
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.upload_box {
    width: 80%;
    display: flex;
    flex-wrap: wrap;
}
.icon_size {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 9999;
    color: #fff;
    font-size: 18px;
    opacity: 0;
    transition: 0.4s;
}
.pic_list {
    position: relative;
    width: 146px;
    height: 146px;
    margin-right: 15px;
    margin-bottom: 15px;
}
.pic_list:hover .icon_size {
    opacity: 1;
}
</style>
<style>
.button_box {
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.BMap_cpyCtrl {
    display: none !important;
}
.anchorBL {
    display: none !important;
}
.amap-logo img {
    display: none !important;
}
.amap-copyright {
    opacity: 0 !important;
}
.custom-checkbox {
  display: flex;
  align-items: center;
}

.custom-checkbox .el-checkbox__input {
  flex-shrink: 0;
}

.custom-checkbox .el-checkbox__label {
  white-space: normal;
  margin-left: 8px; /* 可选：调整文字和复选框之间的间距 */
}
</style>
