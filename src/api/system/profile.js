import request from '../http.js'
import {CONSOLE_BASE_URL} from '../config'
import qs from 'qs';

//用户管理-基础信息
export const getUserInfo = (data) => {
    return request({
        url: CONSOLE_BASE_URL + `platform/user/getBaseUserInfo`,
        method: 'post',
        data
    })
}

//获取短信验证码
export const getPhoneSMSCode = (data) => {
    return request({
        url: CONSOLE_BASE_URL + `platform/user/getPhoneSMSCode?${qs.stringify(data)}`,
        method: 'post',
    })
}

//用户管理-更新基础信息
export const updateUserInfo = (data) => {
    return request({
        url: CONSOLE_BASE_URL + `platform/user/updateBaseInfo`,
        method: 'post',
        data
    })
}

//用户管理-更换头像
export const updateUserAvatar = (data) => {
    return request({
        url: CONSOLE_BASE_URL + `platform/user/updateForAvatar?avatar=${data}`,
        method: 'post',
    })
}

//用户管理-修改用户密码
export const updateUserPwd = (data) => {
    return request({
        url: CONSOLE_BASE_URL + `platform/user/updateForPwd`,
        method: 'post',
        data
    })
}
//设置用户安全吗
export const setSaveUserPwd = (data) => {
    return request({
        url: CONSOLE_BASE_URL + `platform/user/userSetOrChangePassword`,
        method: 'post',
        data
    })
}
