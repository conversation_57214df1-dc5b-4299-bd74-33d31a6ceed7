import OSS from 'ali-oss';
// import { bucket} from '@/api/baseUrl';
const bucket = process.env.NODE_ENV === 'prod' ? 'xytb-prd' : 'xytb-test';
const accessKeyId = process.env.NODE_ENV === 'prod' ? 'LTAI5tJk6Krb6ykSHcpL4GMm' : 'LTAI5tEMi64S5Te4atNifNJW';
const accessKeySecret = process.env.NODE_ENV === 'prod' ? '******************************' : '******************************';
export function client() {
    var client = new OSS({
        bucket: bucket,
        endpoint: 'oss-cn-hangzhou.aliyuncs.com',
        accessKeyId: accessKeyId,
        accessKeySecret: accessKeySecret
    });
    return client;
}
/**
 * 生成随机uuid
 */
export const getFileNameUUID = () => {
    function rx() {
        return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
    }
    return `${+new Date()}_${rx()}${rx()}`;
};
/**
 * 获取今天日期
 */

export const getTimeNow = () => {
    var now = new Date();
    var year = now.getFullYear(); //得到年份
    var month = now.getMonth() + 1; //得到月份
    var date = now.getDate(); //得到日期
    if (month < 10) month = '0' + month;
    return year + month + date;
};
